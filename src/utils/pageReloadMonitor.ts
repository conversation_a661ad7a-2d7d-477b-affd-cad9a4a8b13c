/**
 * Page Reload Monitor
 * Helps identify what's causing unexpected page reloads when switching tabs
 */

export class PageReloadMonitor {
  private static isInitialized = false;
  private static reloadSources: string[] = [];

  /**
   * Initialize monitoring for page reload triggers
   */
  static initialize(): void {
    if (this.isInitialized) return;

    console.log('[PageReloadMonitor] Initializing page reload monitoring...');

    // Monitor window.location.reload calls
    const originalReload = window.location.reload;
    window.location.reload = function(...args) {
      const stack = new Error().stack;
      console.warn('[PageReloadMonitor] Page reload triggered!', {
        source: 'window.location.reload',
        stack: stack,
        timestamp: new Date().toISOString()
      });
      PageReloadMonitor.reloadSources.push(`window.location.reload at ${new Date().toISOString()}`);
      return originalReload.apply(this, args);
    };

    // Monitor visibility change events
    document.addEventListener('visibilitychange', () => {
      console.log('[PageReloadMonitor] Visibility changed:', {
        hidden: document.hidden,
        visibilityState: document.visibilityState,
        timestamp: new Date().toISOString()
      });
    });

    // Monitor focus/blur events
    window.addEventListener('focus', () => {
      console.log('[PageReloadMonitor] Window focused at', new Date().toISOString());
    });

    window.addEventListener('blur', () => {
      console.log('[PageReloadMonitor] Window blurred at', new Date().toISOString());
    });

    // Monitor beforeunload events
    window.addEventListener('beforeunload', (event) => {
      console.log('[PageReloadMonitor] Before unload triggered:', {
        reason: 'beforeunload',
        timestamp: new Date().toISOString(),
        reloadSources: PageReloadMonitor.reloadSources
      });
    });

    // Monitor unload events
    window.addEventListener('unload', () => {
      console.log('[PageReloadMonitor] Unload triggered at', new Date().toISOString());
    });

    // Monitor popstate events (back/forward navigation)
    window.addEventListener('popstate', (event) => {
      console.log('[PageReloadMonitor] Popstate event:', {
        state: event.state,
        timestamp: new Date().toISOString()
      });
    });

    // Monitor storage events
    window.addEventListener('storage', (event) => {
      console.log('[PageReloadMonitor] Storage event:', {
        key: event.key,
        oldValue: event.oldValue,
        newValue: event.newValue,
        timestamp: new Date().toISOString()
      });
    });

    this.isInitialized = true;
    console.log('[PageReloadMonitor] Page reload monitoring initialized');
  }

  /**
   * Get reload sources for debugging
   */
  static getReloadSources(): string[] {
    return [...this.reloadSources];
  }

  /**
   * Clear reload sources
   */
  static clearReloadSources(): void {
    this.reloadSources = [];
  }

  /**
   * Log a potential reload trigger
   */
  static logPotentialTrigger(source: string, details?: any): void {
    console.log(`[PageReloadMonitor] Potential reload trigger: ${source}`, details);
    this.reloadSources.push(`${source} at ${new Date().toISOString()}`);
  }
}

// Expose globally for debugging
(window as any).PageReloadMonitor = PageReloadMonitor;
